{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Bizness\\7thSenseMediaLabz\\Chicas Chicken Flutter\\build\\.cxx\\Debug\\5q3r5d1m\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Bizness\\7thSenseMediaLabz\\Chicas Chicken Flutter\\build\\.cxx\\Debug\\5q3r5d1m\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}